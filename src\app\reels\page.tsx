import { Metadata } from 'next';
import { getReels, getFeaturedReels } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ScrollToTop from '@/components/layout/ScrollToTop';
import ReelsPageClient from './ReelsPageClient';

export const metadata: Metadata = {
  title: 'YouTube Shorts - Creative Short-Form Content',
  description: 'Watch all my YouTube Shorts showcasing dynamic editing, storytelling, and creative content. Professional short-form video portfolio by Uttam Rimal.',
  openGraph: {
    title: 'YouTube Shorts - Uttam Rimal',
    description: 'Watch all my YouTube Shorts showcasing dynamic editing, storytelling, and creative content. Professional short-form video portfolio by Uttam Rimal.',
  },
};

export default async function ShortsPage() {
  // Fetch data on the server side
  const [allReels, featuredReels] = await Promise.all([
    getReels(),
    getFeaturedReels(),
  ]);

  // Ensure all reels have thumbnails
  const allShortsWithThumbnails = allReels.map(reel => ({
    ...reel,
    thumbnail: reel.thumbnail || `https://img.youtube.com/vi/${reel.id}/maxresdefault.jpg`
  }));

  const featuredShortsWithThumbnails = featuredReels.map(reel => ({
    ...reel,
    thumbnail: reel.thumbnail || `https://img.youtube.com/vi/${reel.id}/maxresdefault.jpg`
  }));

  return (
    <main>
      <Header />
      <ReelsPageClient
        allReels={allShortsWithThumbnails}
        featuredReels={featuredShortsWithThumbnails}
      />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
