import Link from 'next/link';
import Image from 'next/image';
import { Calendar, Clock, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { formatDate, type BlogPost } from '@/lib/api';

interface PostCardProps {
  post: BlogPost;
  variant?: 'default' | 'featured' | 'compact';
  showExcerpt?: boolean;
  showAuthor?: boolean;
  className?: string;
}

export default function PostCard({
  post,
  variant = 'default',
  showExcerpt = true,
  showAuthor = true,
  className = ''
}: PostCardProps) {
  const cardClasses = {
    default: 'bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',
    featured: 'bg-white rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 border-2 border-accent',
    compact: 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'
  };

  const imageClasses = {
    default: 'aspect-video',
    featured: 'aspect-video',
    compact: 'aspect-[4/3]'
  };

  const contentClasses = {
    default: 'p-6',
    featured: 'p-8',
    compact: 'p-4'
  };

  const titleClasses = {
    default: 'text-xl font-heading font-semibold text-portfolio-primary mb-3',
    featured: 'text-2xl font-heading font-bold text-portfolio-primary mb-4',
    compact: 'text-lg font-heading font-semibold text-portfolio-primary mb-2'
  };

  return (
    <article className={`group ${cardClasses[variant]} ${className}`}>
      <Link href={`/blog/${post.slug}`}>
        {/* Featured Image */}
        <div className={`relative ${imageClasses[variant]} overflow-hidden`}>
          {post.thumbnail ? (
            <Image
              src={post.thumbnail}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
              <div className="text-white text-6xl font-heading font-bold opacity-20">
                {post.title.charAt(0)}
              </div>
            </div>
          )}

          {/* Overlay with badges */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Category Badge */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-portfolio-secondary/90 text-white backdrop-blur-sm">
              {post.category}
            </Badge>
          </div>

          {/* Featured Badge */}
          {post.featured && (
            <div className="absolute top-4 right-4">
              <Badge variant="default" className="bg-portfolio-accent text-white backdrop-blur-sm">
                Featured
              </Badge>
            </div>
          )}
        </div>

        {/* Content */}
        <div className={contentClasses[variant]}>
          {/* Title */}
          <h3 className={`${titleClasses[variant]} group-hover:text-portfolio-secondary transition-colors duration-300 line-clamp-2`}>
            {post.title}
          </h3>

          {/* Meta Information */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-3">
            <div className="flex items-center gap-1">
              <Calendar size={14} />
              <span>{formatDate(post.createdAt)}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock size={14} />
              <span>{post.readTime} min read</span>
            </div>
            {showAuthor && (
              <div className="flex items-center gap-1">
                <User size={14} />
                <span>Uttam Rimal</span>
              </div>
            )}
          </div>

          {/* Excerpt */}
          {showExcerpt && post.excerpt && (
            <p className={`text-muted-foreground leading-relaxed mb-4 ${variant === 'compact' ? 'text-sm line-clamp-2' : 'line-clamp-3'
              }`}>
              {post.excerpt}
            </p>
          )}

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.slice(0, variant === 'compact' ? 2 : 3).map((tag) => (
                <Badge key={tag} variant="outline" className="text-xs hover:bg-portfolio-primary hover:text-white transition-colors">
                  #{tag}
                </Badge>
              ))}
              {post.tags.length > (variant === 'compact' ? 2 : 3) && (
                <Badge variant="outline" className="text-xs">
                  +{post.tags.length - (variant === 'compact' ? 2 : 3)}
                </Badge>
              )}
            </div>
          )}

          {/* Read More Link */}
          <div className="flex items-center justify-between">
            <span className="text-portfolio-primary font-semibold text-sm group-hover:text-portfolio-secondary transition-colors duration-300">
              Read More →
            </span>

            {/* Status indicator for featured variant */}
            {variant === 'featured' && (
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${post.status === 'published' ? 'bg-green-500' : 'bg-yellow-500'
                  }`} />
                <span className="text-xs text-muted-foreground capitalize">
                  {post.status}
                </span>
              </div>
            )}
          </div>
        </div>
      </Link>
    </article>
  );
}
