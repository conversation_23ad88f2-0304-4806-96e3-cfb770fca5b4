import Image from 'next/image';
import { ExternalLink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type Client } from '@/lib/api';
import clsx from 'clsx';

interface ClientCardProps {
  client: Client;
  variant?: 'logo' | 'detailed' | 'compact';
  onClick?: () => void;
  showWebsite?: boolean;
  className?: string;
}

const cardStyles = {
  logo: 'group relative bg-white p-4 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-full h-full flex items-center justify-center',
  detailed: 'bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105',
  compact: 'bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300'
};

const imageSizes = {
  logo: { className: 'relative h-16 w-full max-w-[120px] mx-auto', size: '16vw' },
  detailed: { className: 'relative h-24 w-full max-w-[200px] mx-auto mb-6', size: '33vw' },
  compact: { className: 'relative h-12 w-full max-w-[100px] mx-auto mb-3', size: '33vw' }
};

const contentPadding = {
  detailed: 'p-6 text-center',
  compact: 'p-4'
};

function ClientImage({ client, variant }: { client: Client; variant: keyof typeof imageSizes }) {
  return (
    <div className={imageSizes[variant].className}>
      <Image
        src={client.logo}
        alt={client.name}
        fill
        className={clsx('object-contain', variant === 'detailed' && 'rounded-md')}
        sizes={imageSizes[variant].size}
      />
    </div>
  );
}

function ClientBadges({ client }: { client: Client }) {
  return (
    <div className="flex flex-wrap justify-center gap-2 mb-4">
      {client.industry && <Badge variant="outline">{client.industry}</Badge>}
      {client.projectType && <Badge variant="secondary">{client.projectType}</Badge>}
    </div>
  );
}

export default function ClientCard({
  client,
  variant = 'logo',
  onClick,
  showWebsite = false,
  className = ''
}: ClientCardProps) {
  if (variant === 'logo') {
    return (
      <button
        onClick={onClick}
        className={clsx(
          'group bg-card rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer w-40 h-40 flex flex-col items-center justify-center text-center',
          className
        )}
        title={client.name}
        aria-label={`View ${client.name}`}
      >
        <div className="relative w-24 h-24 mb-4">
          <Image
            src={client.logo}
            alt={client.name}
            fill
            className="object-cover rounded-full border border-gray-200 shadow-sm"
            sizes="96px"
          />
        </div>
        <p className="text-sm font-medium text-gray-700">{client.name}</p>
      </button>
    );
  }
  if (variant === 'detailed') {
    return (
      <article
        className={clsx(
          'bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 w-full max-w-md overflow-hidden',
          className
        )}
      >
        <div className="p-6 flex flex-col items-center text-center">
          <div className="relative w-28 h-28 mb-4">
            <Image
              src={client.logo}
              alt={client.name}
              fill
              className="object-cover rounded-xl border border-gray-200 shadow-sm"
              sizes="112px"
            />
          </div>

          <h3 className="text-2xl font-heading font-bold text-portfolio-primary mb-2">
            {client.name}
          </h3>

          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {client.industry && <Badge variant="outline">{client.industry}</Badge>}
            {client.projectType && <Badge variant="secondary">{client.projectType}</Badge>}
          </div>

          <p className="text-muted-foreground leading-relaxed mb-6">
            {client.description}
          </p>

          {showWebsite && client.website && (
            <Button variant="outline" asChild>
              <a
                href={client.website}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2"
              >
                Visit Website <ExternalLink size={16} />
              </a>
            </Button>
          )}
        </div>
      </article>
    );
  }


  // Compact
  return (
    <article className={clsx(cardStyles.compact, className)}>
      <div className={contentPadding.compact}>
        <ClientImage client={client} variant="compact" />
        <h4 className="text-lg font-heading font-semibold text-foreground mb-2">{client.name}</h4>
        <ClientBadges client={client} />
        <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
          {client.description}
        </p>
        <div className="flex items-center justify-between">
          {onClick && (
            <button
              onClick={onClick}
              className="text-foreground font-semibold text-sm hover:text-accent transition-colors duration-300"
              aria-label={`Learn more about ${client.name}`}
            >
              Learn More →
            </button>
          )}
          {showWebsite && client.website && (
            <a
              href={client.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-portfolio-secondary hover:text-portfolio-primary transition-colors duration-300"
              aria-label={`Visit ${client.name} website`}
            >
              <ExternalLink size={16} />
            </a>
          )}
        </div>
      </div>
    </article>
  );
}
