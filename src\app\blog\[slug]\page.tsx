import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getBlogPostBySlug, getBlogPosts } from '@/lib/api';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ScrollToTop from '@/components/layout/ScrollToTop';
import BlogDetailClient from './BlogDetailClient';

interface BlogPostPageProps {
  params: Promise<{ slug: string }>;
}

export async function generateStaticParams() {
  try {
    const posts = await getBlogPosts();
    return posts.map((post) => ({
      slug: post.slug,
    }));
  } catch (error) {
    console.error('Error generating static params:', error);
    return [];
  }
}

// Enable ISR (Incremental Static Regeneration) for dynamic content
export const revalidate = 3600; // Revalidate every hour

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const { slug } = await params;
    const post = await getBlogPostBySlug(slug);

    if (!post) {
      return {
        title: 'Post Not Found - Uttam Rimal',
        description: 'The blog post you are looking for could not be found.',
      };
    }

    return {
      title: `${post.title} - Uttam Rimal`,
      description: post.excerpt,
      keywords: post.tags?.join(', '),
      authors: [{ name: 'Uttam Rimal' }],
      openGraph: {
        title: post.title,
        description: post.excerpt,
        type: 'article',
        publishedTime: post.createdAt,
        authors: ['Uttam Rimal'],
        tags: post.tags,
        images: post.thumbnail ? [
          {
            url: post.thumbnail,
            width: 1200,
            height: 630,
            alt: post.title,
          }
        ] : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: post.title,
        description: post.excerpt,
        images: post.thumbnail ? [post.thumbnail] : [],
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Blog Post - Uttam Rimal',
      description: 'Professional video editing blog by Uttam Rimal.',
    };
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;

  try {
    // Fetch the main post and related posts on the server
    const [post, allPosts] = await Promise.all([
      getBlogPostBySlug(slug),
      getBlogPosts()
    ]);

    if (!post) {
      notFound();
    }

    // Get related posts on the server
    const relatedPosts = allPosts
      .filter((relatedPost) =>
        relatedPost._id !== post._id &&
        relatedPost.category === post.category
      )
      .slice(0, 3);

    return (
      <main>
        <Header />
        <BlogDetailClient post={post} relatedPosts={relatedPosts} />
        <Footer />
        <ScrollToTop />
      </main>
    );
  } catch (error) {
    console.error('Error fetching blog post:', error);
    notFound();
  }
}
