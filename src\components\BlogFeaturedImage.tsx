import Image from 'next/image';
import { type BlogPost } from '@/lib/api';

interface BlogFeaturedImageProps {
  post: BlogPost;
}

export default function BlogFeaturedImage({ post }: BlogFeaturedImageProps) {
  if (!post.thumbnail) {
    return null;
  }

  return (
    <div className="relative">
      {/* Featured Image Container */}
      <div className="relative aspect-video max-h-[500px] overflow-hidden rounded-b-2xl shadow-2xl">
        <Image
          src={post.thumbnail}
          alt={post.title}
          fill
          className="object-cover transition-transform duration-700 hover:scale-105"
          priority
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
        />
        
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
        
        {/* Image Caption Overlay */}
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
            <p className="text-sm text-gray-700 font-medium">
              Featured image for "{post.title}"
            </p>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute -top-4 -left-4 w-8 h-8 bg-portfolio-primary rounded-full opacity-20"></div>
      <div className="absolute -top-2 -right-6 w-6 h-6 bg-portfolio-secondary rounded-full opacity-30"></div>
      <div className="absolute -bottom-3 -left-2 w-4 h-4 bg-portfolio-accent rounded-full opacity-25"></div>
    </div>
  );
}
