import { Metadata } from 'next';
import { getBlogPosts, getFeaturedBlogPosts, formatDate } from '@/lib/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import BlogPageClient from './BlogPageClient';

export const metadata: Metadata = {
  title: 'Blog - Insights & Tips',
  description: 'Video editing tutorials, tips, and insights from professional video editor <PERSON><PERSON><PERSON> Rimal.',
  openGraph: {
    title: 'Blog - Uttam Rimal',
    description: 'Video editing tutorials, tips, and insights from professional video editor Utta<PERSON> Rimal.',
  },
};

export default async function BlogPage() {
  // Fetch data on the server side
  const [allPosts, featuredPosts] = await Promise.all([
    getBlogPosts(),
    getFeaturedBlogPosts(),
  ]);

  return (
    <main>
      <Header />
      <BlogPageClient allPosts={allPosts} featuredPosts={featuredPosts} />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
