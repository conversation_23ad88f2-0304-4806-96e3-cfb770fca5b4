'use client';

import { type BlogPost } from '@/lib/api';
import BlogDetailHeader from '../BlogDetailHeader';
import BlogDetailContent from '../BlogDetailContent';
import BlogDetailSidebar from '../BlogDetailSidebar';
import FeaturedImage from '../BlogFeaturedImage';

interface BlogDetailProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
}

export default function BlogDetail({ post, relatedPosts }: BlogDetailProps) {
  return (
    <div className="min-h-screen bg-portfolio-light">
      {/* Header */}
      <BlogDetailHeader post={post} />

      {/* Featured Image */}
      <FeaturedImage post={post} />

      {/* Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row gap-12">
            {/* Main Content */}
            <BlogDetailContent post={post} />

            {/* Sidebar */}
            <BlogDetailSidebar relatedPosts={relatedPosts} />
          </div>
        </div>
      </div>
    </div>
  );
}
