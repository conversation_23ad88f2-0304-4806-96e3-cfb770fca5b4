'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';

const navigation = [
  { name: 'Home', href: '#home' },
  {
    name: 'Videos',
    href: '#featured-work',
    dropdown: [
      { name: 'Featured Videos', href: '#featured-work' },
      { name: 'View All Videos', href: '/videos' }
    ]
  },
  { name: 'Clients', href: '#clients' },
  {
    name: '<PERSON><PERSON>',
    href: '#reels',
    dropdown: [
      { name: 'Featured Reels', href: '#reels' },
      { name: 'View All Reels', href: '/reels' }
    ]
  },
  {
    name: 'Blog',
    href: '#blog',
    dropdown: [
      { name: 'Latest Posts', href: '#blog' },
      { name: 'View All Posts', href: '/blog' }
    ]
  },
  { name: 'Testimonials', href: '#testimonials' },
  { name: 'Contact', href: '#contact' },
];

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);

      // Update active section based on scroll position
      const sections = navigation.map(item => item.href.substring(1));
      const currentSection = sections.find(section => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });

      if (currentSection) {
        setActiveSection(currentSection);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href: string) => {
    if (href.startsWith('#')) {
      // Handle anchor links
      const targetId = href.substring(1);
      const element = document.getElementById(targetId);
      if (element) {
        const headerOffset = 80;
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    } else {
      // Handle regular page navigation
      window.location.href = href;
    }
    setIsMobileMenuOpen(false);
    setOpenDropdown(null);
  };

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled
      ? 'bg-background/95 backdrop-blur-md shadow-lg border-b border-foreground/10'
      : 'bg-background/90 backdrop-blur-sm'
      }`}>
      <nav className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link
            href="#home"
            className="text-2xl font-heading font-bold text-foreground hover:text-accent transition-colors duration-300 hover:scale-105 transform"
            onClick={(e) => {
              e.preventDefault();
              handleNavClick('#home');
            }}
          >
            UR
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <div key={item.name} className="relative group">
                {item.dropdown ? (
                  <>
                    <button
                      onClick={() => handleNavClick(item.href)}
                      onMouseEnter={() => setOpenDropdown(item.name)}
                      onMouseLeave={() => setOpenDropdown(null)}
                      className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 flex items-center gap-1 ${activeSection === item.href.substring(1)
                        ? 'text-accent'
                        : 'text-foreground hover:text-accent'
                        }`}
                    >
                      {item.name}
                      <ChevronDown size={14} className={`transition-transform duration-200 ${openDropdown === item.name ? 'rotate-180' : ''
                        }`} />
                      <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'
                        }`} />
                    </button>

                    {/* Dropdown Menu */}
                    <div
                      className={`absolute top-full left-0 mt-2 w-48 bg-background/95 backdrop-blur-md border border-foreground/10 rounded-lg shadow-lg transition-all duration-200 ${openDropdown === item.name ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                        }`}
                      onMouseEnter={() => setOpenDropdown(item.name)}
                      onMouseLeave={() => setOpenDropdown(null)}
                    >
                      {item.dropdown.map((dropdownItem) => (
                        <button
                          key={dropdownItem.name}
                          onClick={() => handleNavClick(dropdownItem.href)}
                          className="block w-full text-left px-4 py-3 text-sm text-foreground hover:text-accent hover:bg-foreground/10 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg"
                        >
                          {dropdownItem.name}
                        </button>
                      ))}
                    </div>
                  </>
                ) : (
                  <button
                    onClick={() => handleNavClick(item.href)}
                    className={`relative text-sm font-semibold transition-colors duration-300 py-2 px-1 ${activeSection === item.href.substring(1)
                      ? 'text-accent'
                      : 'text-foreground hover:text-accent'
                      }`}
                  >
                    {item.name}
                    <span className={`absolute bottom-0 left-0 h-0.5 bg-accent transition-all duration-300 ${activeSection === item.href.substring(1) ? 'w-full' : 'w-0 group-hover:w-full'
                      }`} />
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden text-foreground hover:text-accent hover:bg-foreground/10"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        <div className={`md:hidden transition-all duration-300 overflow-hidden ${isMobileMenuOpen ? 'max-h-[500px] opacity-100' : 'max-h-0 opacity-0'
          }`}>
          <div className="py-4 space-y-2 border-t border-foreground/10 mt-4">
            {navigation.map((item) => (
              <div key={item.name}>
                <button
                  onClick={() => handleNavClick(item.href)}
                  className={`block w-full text-left px-4 py-3 text-sm font-semibold transition-colors duration-300 rounded-lg ${activeSection === item.href.substring(1)
                    ? 'text-accent bg-foreground/10'
                    : 'text-foreground hover:text-accent hover:bg-foreground/5'
                    }`}
                >
                  {item.name}
                </button>

                {/* Mobile Dropdown Items */}
                {item.dropdown && (
                  <div className="ml-4 mt-1 space-y-1">
                    {item.dropdown.map((dropdownItem) => (
                      <button
                        key={dropdownItem.name}
                        onClick={() => handleNavClick(dropdownItem.href)}
                        className="block w-full text-left px-4 py-2 text-xs text-foreground/80 hover:text-accent hover:bg-foreground/5 transition-colors duration-300 rounded-lg"
                      >
                        {dropdownItem.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </nav>
    </header>
  );
}
