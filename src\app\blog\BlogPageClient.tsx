'use client';

import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import PostCard from '@/components/features/blog/BlogCard';
import { type BlogPost } from '@/lib/api';

interface BlogPageClientProps {
  allPosts: BlogPost[];
  featuredPosts: BlogPost[];
}

export default function BlogPageClient({ allPosts, featuredPosts }: BlogPageClientProps) {
  return (
    <div className="min-h-screen bg-portfolio-light">
      {/* Header */}
      <div className="bg-portfolio-primary text-white py-20">
        <div className="container mx-auto px-4">
          <Link
            href="/#blog"
            className="inline-flex items-center gap-2 text-portfolio-accent hover:text-white transition-colors mb-8"
          >
            <ArrowLeft size={20} />
            Back to Portfolio
          </Link>

          <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4">
            Blog & Insights
          </h1>
          <p className="text-xl text-white/90 max-w-2xl">
            Sharing knowledge on video editing, storytelling, and creative techniques.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <section className="mb-16">
            <h2 className="text-3xl font-heading font-bold text-portfolio-primary mb-8">
              Featured Posts
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {featuredPosts.map((post) => (
                <PostCard
                  key={post._id}
                  post={post}
                  variant="featured"
                  showExcerpt={true}
                  showAuthor={true}
                />
              ))}
            </div>
          </section>
        )}

        {/* All Posts */}
        <section>
          <h2 className="text-3xl font-heading font-bold text-portfolio-primary mb-8">
            {featuredPosts.length > 0 ? 'All Posts' : 'Latest Posts'}
          </h2>

          {allPosts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-lg text-muted-foreground">No blog posts available at the moment.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {allPosts.map((post) => (
                <PostCard
                  key={post._id}
                  post={post}
                  variant="default"
                  showExcerpt={true}
                  showAuthor={false}
                />
              ))}
            </div>
          )}
        </section>
      </div>
    </div>
  );
}
