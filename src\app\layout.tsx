import type { Metadata } from "next";
import { Inter, Playfair_Display, Space_Grotesk } from "next/font/google";
import "./globals.css";
import WhatsAppButton from "@/components/common/WhatsAppButton";

const inter = Inter({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-body",
  display: "swap",
});

const playfairDisplay = Playfair_Display({
  subsets: ["latin"],
  weight: ["400", "600", "700"],
  variable: "--font-heading",
  display: "swap",
});

const spaceGrotesk = Space_Grotesk({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-accent",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Uttam Rimal - Professional Video Editor Portfolio",
  description: "Creative Video Editor & Storyteller. Transforming footage into compelling narratives with over a year of experience crafting visually stunning videos that engage and inspire.",
  keywords: "video editor, video editing, storytelling, motion graphics, post-production, creative editing, Nepal, freelance",
  authors: [{ name: "Uttam Rimal" }],
  creator: "Uttam Rimal",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://uttamrimal.com",
    title: "Uttam Rimal - Professional Video Editor Portfolio",
    description: "Creative Video Editor & Storyteller. Transforming footage into compelling narratives.",
    siteName: "Uttam Rimal Portfolio",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Uttam Rimal - Video Editor Portfolio",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Uttam Rimal - Professional Video Editor Portfolio",
    description: "Creative Video Editor & Storyteller. Transforming footage into compelling narratives.",
    images: ["/images/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#1a2a6c" />
      </head>
      <body
        className={`${inter.variable} ${playfairDisplay.variable} ${spaceGrotesk.variable} antialiased`}
      >
        {children}
        <WhatsAppButton />
      </body>
    </html>
  );
}
