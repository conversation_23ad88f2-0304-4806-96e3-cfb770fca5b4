import { Metadata } from 'next';
import { getVideos, getFeaturedVideos } from '@/lib/api';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ScrollToTop from '@/components/ScrollToTop';
import VideosPageClient from './VideosPageClient';

export const metadata: Metadata = {
  title: 'Videos - Portfolio Showcase',
  description: 'Watch all my video editing work, tutorials, and creative projects. Professional video editing portfolio by Uttam Rimal.',
  openGraph: {
    title: 'Videos - Uttam Rimal',
    description: 'Watch all my video editing work, tutorials, and creative projects. Professional video editing portfolio by Uttam Rimal.',
  },
};

export default async function VideosPage() {
  // Fetch data on the server side
  const [allVideos, featuredVideos] = await Promise.all([
    getVideos(),
    getFeaturedVideos(),
  ]);

  return (
    <main>
      <Header />
      <VideosPageClient allVideos={allVideos} featuredVideos={featuredVideos} />
      <Footer />
      <ScrollToTop />
    </main>
  );
}
