import Link from 'next/link';
import { Mail, Phone, MapPin, Youtube, Instagram, Linkedin } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-portfolio-secondary text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-1">
            <h3 className="text-2xl font-heading font-bold mb-4">Uttam Rimal</h3>
            <p className="text-white/80 mb-4">
              Professional video editor creating compelling visual stories that engage and inspire audiences.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white/80 hover:text-white transition-colors">
                <Youtube size={20} />
              </a>
              <a href="#" className="text-white/80 hover:text-white transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-white/80 hover:text-white transition-colors">
                <Linkedin size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-white/80 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/videos" className="text-white/80 hover:text-white transition-colors">
                  Videos
                </Link>
              </li>
              <li>
                <Link href="/reels" className="text-white/80 hover:text-white transition-colors">
                  Reels
                </Link>
              </li>
              <li>
                <Link href="/clients" className="text-white/80 hover:text-white transition-colors">
                  Clients
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-white/80 hover:text-white transition-colors">
                  Blog
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Services</h4>
            <ul className="space-y-2 text-white/80">
              <li>Video Editing</li>
              <li>Color Grading</li>
              <li>Motion Graphics</li>
              <li>Social Media Content</li>
              <li>YouTube Editing</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail size={16} />
                <span className="text-white/80"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone size={16} />
                <span className="text-white/80">+****************</span>
              </div>
              <div className="flex items-center gap-3">
                <MapPin size={16} />
                <span className="text-white/80">New York, NY</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-white/20 mt-8 pt-8 text-center">
          <p className="text-white/60">
            © {new Date().getFullYear()} Uttam Rimal. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
