'use client';

import { Share2, Co<PERSON>, Check } from 'lucide-react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { type BlogPost } from '@/lib/api';

interface BlogDetailContentProps {
  post: BlogPost;
}

export default function BlogDetailContent({ post }: BlogDetailContentProps) {
  const [copied, setCopied] = useState(false);

  const sharePost = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to copying URL to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (error) {
        console.log('Error copying to clipboard:', error);
      }
    }
  };

  // Enhanced content processing for better typography
  const processContent = (content: string) => {
    return content
      .replace(/\n\n/g, '</p><p class="mb-6">')
      .replace(/\n/g, '<br />')
      .replace(/#{1,6}\s(.+)/g, '<h2 class="text-2xl md:text-3xl font-heading font-bold text-portfolio-primary mt-12 mb-6 first:mt-0">$1</h2>')
      .replace(/\*\*(.+?)\*\*/g, '<strong class="font-semibold text-portfolio-primary">$1</strong>')
      .replace(/\*(.+?)\*/g, '<em class="italic">$1</em>')
      .replace(/`(.+?)`/g, '<code class="bg-gray-100 px-2 py-1 rounded text-sm font-mono">$1</code>');
  };

  return (
    <article className="flex-1">
      {/* Main Content */}
      <div className="prose prose-lg max-w-none">
        <div
          className="text-portfolio-text leading-relaxed text-lg"
          dangerouslySetInnerHTML={{
            __html: `<p class="mb-6">${processContent(post.content)}</p>`
          }}
        />
      </div>

      {/* Content Footer */}
      <div className="mt-16 space-y-8">
        {/* Tags Section */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-2 bg-portfolio-primary/10 rounded-lg">
              <span className="text-portfolio-primary text-lg">🏷️</span>
            </div>
            <h3 className="text-xl font-heading font-semibold text-portfolio-primary">
              Article Tags
            </h3>
          </div>
          <div className="flex flex-wrap gap-3">
            {post.tags.map((tag) => (
              <Badge 
                key={tag} 
                variant="outline" 
                className="hover:bg-portfolio-primary hover:text-white transition-all duration-300 hover:scale-105 cursor-pointer px-4 py-2"
              >
                #{tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Share Section */}
        <div className="bg-gradient-to-r from-portfolio-light to-white rounded-xl shadow-sm border p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div>
              <h3 className="text-xl font-heading font-semibold text-portfolio-primary mb-2">
                Share this article
              </h3>
              <p className="text-muted-foreground">
                Found this helpful? Share it with others who might benefit!
              </p>
            </div>
            <Button
              onClick={sharePost}
              className="bg-portfolio-primary hover:bg-portfolio-primary/90 flex items-center gap-2 min-w-[120px]"
            >
              {copied ? (
                <>
                  <Check size={16} />
                  Copied!
                </>
              ) : (
                <>
                  <Share2 size={16} />
                  Share
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-portfolio-primary to-portfolio-secondary text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-heading font-bold mb-4">
            Ready to Transform Your Content?
          </h3>
          <p className="text-white/90 mb-6 max-w-2xl mx-auto">
            Let's work together to create compelling videos that engage your audience and tell your story effectively.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              asChild
              variant="secondary"
              className="bg-white text-portfolio-primary hover:bg-white/90"
            >
              <a href="/#contact">Get in Touch</a>
            </Button>
            <Button 
              asChild
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-portfolio-primary"
            >
              <a href="/videos">View Portfolio</a>
            </Button>
          </div>
        </div>
      </div>
    </article>
  );
}
